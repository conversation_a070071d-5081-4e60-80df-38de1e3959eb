package com.tqhit.battery.one.firebase

import android.content.Context
import android.util.Log
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import java.lang.reflect.Field
import javax.inject.Inject
import javax.inject.Singleton

/**
 * FirebaseRemoteConfigHelperReplacer - Utility class that can replace FirebaseRemoteConfigHelper
 * instances in objects with our safe lazy provider instances.
 * 
 * This class uses reflection to find and replace FirebaseRemoteConfigHelper fields in objects
 * with our safe implementation that waits for Firebase to be ready.
 */
@Singleton
class FirebaseRemoteConfigHelperReplacer @Inject constructor(
    private val context: Context,
    private val firebaseReadinessManager: FirebaseReadinessManager
) {
    
    companion object {
        private const val TAG = "FirebaseHelperReplacer"
    }
    
    private val lazyProvider by lazy {
        LazyFirebaseRemoteConfigProvider(context, firebaseReadinessManager)
    }
    
    /**
     * Replace all FirebaseRemoteConfigHelper fields in the given object with our safe provider
     */
    fun replaceInObject(target: Any) {
        try {
            val clazz = target.javaClass
            val fields = getAllFields(clazz)
            
            for (field in fields) {
                if (field.type == FirebaseRemoteConfigHelper::class.java) {
                    field.isAccessible = true
                    val currentValue = field.get(target)
                    
                    if (currentValue != null) {
                        Log.d(TAG, "Replacing FirebaseRemoteConfigHelper in ${clazz.simpleName}.${field.name}")
                        
                        // Replace with our safe provider's helper
                        val safeHelper = lazyProvider.get()
                        field.set(target, safeHelper)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing FirebaseRemoteConfigHelper in ${target.javaClass.simpleName}", e)
        }
    }
    
    /**
     * Get all fields including inherited ones
     */
    private fun getAllFields(clazz: Class<*>): List<Field> {
        val fields = mutableListOf<Field>()
        var currentClass: Class<*>? = clazz
        
        while (currentClass != null && currentClass != Any::class.java) {
            fields.addAll(currentClass.declaredFields)
            currentClass = currentClass.superclass
        }
        
        return fields
    }
}
