package com.tqhit.battery.one.firebase

import android.content.Context
import android.content.res.Resources
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import org.xmlpull.v1.XmlPullParser
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SafeFirebaseRemoteConfigHelper - Wrapper class that provides safe access to Firebase Remote Config
 * with fallback to default values from remote_config_defaults.xml.
 * 
 * This wrapper ensures the app continues to function with sensible defaults while Firebase
 * initializes asynchronously.
 */
@Singleton
class SafeFirebaseRemoteConfigHelper @Inject constructor(
    private val context: Context,
    private val firebaseReadinessManager: FirebaseReadinessManager
) {
    
    companion object {
        private const val TAG = "SafeFirebaseRemoteConfigHelper"
    }
    
    // Cache for default values from XML
    private val defaultValues = mutableMapOf<String, String>()
    private var defaultsLoaded = false
    
    // Lazy delegate to the real FirebaseRemoteConfigHelper
    private val realFirebaseHelper: FirebaseRemoteConfigHelper? by lazy {
        if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfigHelper(FirebaseRemoteConfig.getInstance())
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error creating FirebaseRemoteConfigHelper", e)
                null
            }
        } else {
            null
        }
    }
    
    init {
        loadDefaultValues()
    }
    
    /**
     * Load default values from remote_config_defaults.xml
     */
    private fun loadDefaultValues() {
        if (defaultsLoaded) return
        
        try {
            val resources = context.resources
            val parser = resources.getXml(R.xml.remote_config_defaults)
            
            var eventType = parser.eventType
            var currentKey: String? = null
            
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_TAG -> {
                        when (parser.name) {
                            "key" -> {
                                parser.next()
                                if (parser.eventType == XmlPullParser.TEXT) {
                                    currentKey = parser.text
                                }
                            }
                            "value" -> {
                                parser.next()
                                if (parser.eventType == XmlPullParser.TEXT && currentKey != null) {
                                    defaultValues[currentKey] = parser.text
                                    currentKey = null
                                }
                            }
                        }
                    }
                }
                eventType = parser.next()
            }
            defaultsLoaded = true
            android.util.Log.d(TAG, "Loaded ${defaultValues.size} default values from XML")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error loading default values from XML", e)
        }
    }
    
    /**
     * Get string value with fallback to default
     */
    override fun getString(key: String): String {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getString(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase string for key: $key, using default", e)
                getDefaultString(key)
            }
        } else {
            getDefaultString(key)
        }
    }
    
    /**
     * Get string value with custom default
     */
    override fun getString(key: String, defaultValue: String): String {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                val firebaseValue = FirebaseRemoteConfig.getInstance().getString(key)
                if (firebaseValue.isNotEmpty()) firebaseValue else defaultValue
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase string for key: $key, using provided default", e)
                defaultValue
            }
        } else {
            getDefaultString(key).takeIf { it.isNotEmpty() } ?: defaultValue
        }
    }
    
    /**
     * Get boolean value with fallback to default
     */
    override fun getBoolean(key: String): Boolean {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getBoolean(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase boolean for key: $key, using default", e)
                getDefaultBoolean(key)
            }
        } else {
            getDefaultBoolean(key)
        }
    }
    
    /**
     * Get boolean value with custom default
     */
    override fun getBoolean(key: String, defaultValue: Boolean): Boolean {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getBoolean(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase boolean for key: $key, using provided default", e)
                defaultValue
            }
        } else {
            getDefaultBoolean(key)
        }
    }
    
    /**
     * Get long value with fallback to default
     */
    override fun getLong(key: String): Long {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getLong(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase long for key: $key, using default", e)
                getDefaultLong(key)
            }
        } else {
            getDefaultLong(key)
        }
    }
    
    /**
     * Get long value with custom default
     */
    override fun getLong(key: String, defaultValue: Long): Long {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getLong(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase long for key: $key, using provided default", e)
                defaultValue
            }
        } else {
            getDefaultLong(key)
        }
    }
    
    /**
     * Get double value with fallback to default
     */
    override fun getDouble(key: String): Double {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getDouble(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase double for key: $key, using default", e)
                getDefaultDouble(key)
            }
        } else {
            getDefaultDouble(key)
        }
    }
    
    /**
     * Get double value with custom default
     */
    override fun getDouble(key: String, defaultValue: Double): Double {
        return if (firebaseReadinessManager.isFirebaseReady()) {
            try {
                FirebaseRemoteConfig.getInstance().getDouble(key)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "Error getting Firebase double for key: $key, using provided default", e)
                defaultValue
            }
        } else {
            getDefaultDouble(key)
        }
    }
    
    // Helper methods to get default values from XML cache
    private fun getDefaultString(key: String): String {
        return defaultValues[key] ?: ""
    }
    
    private fun getDefaultBoolean(key: String): Boolean {
        return defaultValues[key]?.toBooleanStrictOrNull() ?: false
    }
    
    private fun getDefaultLong(key: String): Long {
        return defaultValues[key]?.toLongOrNull() ?: 0L
    }
    
    private fun getDefaultDouble(key: String): Double {
        return defaultValues[key]?.toDoubleOrNull() ?: 0.0
    }
}
