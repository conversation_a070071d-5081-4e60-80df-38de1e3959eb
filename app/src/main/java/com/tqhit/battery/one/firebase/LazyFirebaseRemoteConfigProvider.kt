package com.tqhit.battery.one.firebase

import android.content.Context
import android.util.Log
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import kotlinx.coroutines.runBlocking
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Provider
import javax.inject.Singleton

/**
 * LazyFirebaseRemoteConfigProvider - Provider that delays instantiation of FirebaseRemoteConfigHelper
 * until Firebase is actually ready.
 *
 * This provider ensures that FirebaseRemoteConfigHelper is only created when Firebase is ready,
 * preventing the crash during Hilt injection. It implements the Provider pattern to delay
 * instantiation and includes timeout handling and fallback mechanisms.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles lazy Firebase Remote Config Helper provision
 * - Open/Closed: Extensible for additional provider features
 * - Dependency Inversion: Provides abstractions for Firebase Remote Config Helper creation
 */
@Singleton
class LazyFirebaseRemoteConfigProvider @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseReadinessManager: FirebaseReadinessManager
) : Provider<FirebaseRemoteConfigHelper> {

    companion object {
        private const val TAG = "LazyFirebaseRemoteConfigProvider"
        private const val TIMEOUT_MS = 5000L // 5 seconds
    }

    @Volatile
    private var cachedHelper: FirebaseRemoteConfigHelper? = null

    /**
     * Gets the FirebaseRemoteConfigHelper instance, creating it only when Firebase is ready.
     * This method implements lazy instantiation with Firebase readiness checking,
     * timeout handling, and fallback to default values.
     *
     * @return FirebaseRemoteConfigHelper instance (real or fallback)
     */
    override fun get(): FirebaseRemoteConfigHelper {
        // Return cached instance if available
        cachedHelper?.let { return it }

        synchronized(this) {
            // Double-check locking pattern
            cachedHelper?.let { return it }

            Log.d(TAG, "Creating FirebaseRemoteConfigHelper, waiting for Firebase readiness...")

            // Wait for Firebase to be ready with timeout
            val isFirebaseReady = runBlocking {
                firebaseReadinessManager.waitForFirebaseReady(TIMEOUT_MS)
            }

            val helper = if (isFirebaseReady) {
                Log.d(TAG, "Firebase is ready, creating real FirebaseRemoteConfigHelper")
                try {
                    // Create FirebaseRemoteConfigHelper with the ready Firebase instance
                    val firebaseConfig = FirebaseRemoteConfig.getInstance()
                    // Load defaults to ensure fallback values are available
                    loadDefaultsIntoFirebase(context, firebaseConfig)
                    FirebaseRemoteConfigHelper(firebaseConfig)
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating FirebaseRemoteConfigHelper, creating with defaults", e)
                    createFirebaseHelperWithDefaults(context)
                }
            } else {
                Log.w(TAG, "Firebase not ready within timeout, creating FirebaseRemoteConfigHelper with defaults")
                createFirebaseHelperWithDefaults(context)
            }

            cachedHelper = helper
            Log.d(TAG, "FirebaseRemoteConfigHelper created and cached successfully")
            return helper
        }
    }

    /**
     * Clears the cached helper instance.
     * This method is primarily for testing purposes and should not be used in production.
     */
    fun clearCache() {
        synchronized(this) {
            Log.d(TAG, "Clearing cached FirebaseRemoteConfigHelper")
            cachedHelper = null
        }
    }

    /**
     * Checks if a helper instance is currently cached.
     *
     * @return true if helper is cached, false otherwise
     */
    fun isCached(): Boolean {
        return cachedHelper != null
    }

    /**
     * Gets the current provider status for debugging.
     *
     * @return String representation of provider status
     */
    fun getProviderStatus(): String {
        val cached = cachedHelper != null
        val firebaseStatus = firebaseReadinessManager.getReadinessStatus()
        return "Provider cached: $cached, $firebaseStatus"
    }

    /**
     * Creates a FirebaseRemoteConfigHelper with default values loaded.
     * This method ensures that even if Firebase isn't fully ready, the helper
     * will return sensible default values from the XML configuration.
     *
     * @param context Application context for loading defaults
     * @return FirebaseRemoteConfigHelper with defaults loaded
     */
    private fun createFirebaseHelperWithDefaults(context: Context): FirebaseRemoteConfigHelper {
        return try {
            val firebaseConfig = FirebaseRemoteConfig.getInstance()
            loadDefaultsIntoFirebase(context, firebaseConfig)
            FirebaseRemoteConfigHelper(firebaseConfig)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create FirebaseRemoteConfigHelper even with defaults", e)
            // As a last resort, try to create with minimal setup
            try {
                val firebaseConfig = FirebaseRemoteConfig.getInstance()
                firebaseConfig.setDefaultsAsync(getHardcodedDefaults())
                FirebaseRemoteConfigHelper(firebaseConfig)
            } catch (e2: Exception) {
                Log.e(TAG, "Complete failure to create FirebaseRemoteConfigHelper", e2)
                throw IllegalStateException("Cannot create FirebaseRemoteConfigHelper", e2)
            }
        }
    }

    /**
     * Load default values from XML into Firebase Remote Config.
     * This ensures that the remote config will return sensible default values.
     *
     * @param context Application context for loading XML
     * @param firebaseConfig Firebase Remote Config instance to load defaults into
     */
    private fun loadDefaultsIntoFirebase(context: Context, firebaseConfig: FirebaseRemoteConfig) {
        try {
            // Try to load from XML resource first
            firebaseConfig.setDefaultsAsync(com.tqhit.battery.one.R.xml.remote_config_defaults)
            Log.d(TAG, "Loaded default values from XML into Firebase Remote Config")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to load defaults from XML, using hardcoded defaults", e)
            // Fallback to hardcoded defaults
            firebaseConfig.setDefaultsAsync(getHardcodedDefaults())
            Log.d(TAG, "Loaded hardcoded default values into Firebase Remote Config")
        }
    }

    /**
     * Get hardcoded default values as ultimate fallback.
     * These values match those in remote_config_defaults.xml.
     *
     * @return Map of hardcoded default values
     */
    private fun getHardcodedDefaults(): Map<String, Any> {
        return mapOf(
            // Ad configuration defaults
            "aoa_enable" to true,
            "aoa_enable_first_session" to false,
            "aoa_show_frequency" to 120L,

            "iv_enable" to true,
            "iv_first_session_load_delay" to 0L,
            "iv_load_delay" to 0L,
            "iv_first_session_show_delay" to 0L,
            "iv_show_delay" to 0L,
            "iv_show_frequency" to 30L,
            "iv_show_delay_after_RV" to 180L,

            "rv_enable" to true,
            "rv_load_in_view" to false,
            "rv_load_delay" to 90L,

            "nt_enable" to true,
            "bn_enable" to true,

            // Animation configuration defaults
            "animation_json" to "[]",

            // Onboarding configuration
            "onboarding_next_delay" to 2000L
        )
    }
}