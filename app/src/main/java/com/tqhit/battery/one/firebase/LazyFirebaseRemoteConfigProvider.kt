package com.tqhit.battery.one.firebase

import android.content.Context
import android.util.Log
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import kotlinx.coroutines.runBlocking
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Provider
import javax.inject.Singleton

/**
 * LazyFirebaseRemoteConfigProvider - Provider that delays instantiation of FirebaseRemoteConfigHelper
 * until Firebase is actually ready.
 *
 * This provider ensures that FirebaseRemoteConfigHelper is only created when Firebase is ready,
 * preventing the crash during Hilt injection. It implements the Provider pattern to delay
 * instantiation and includes timeout handling and fallback mechanisms.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles lazy Firebase Remote Config Helper provision
 * - Open/Closed: Extensible for additional provider features
 * - Dependency Inversion: Provides abstractions for Firebase Remote Config Helper creation
 */
@Singleton
class LazyFirebaseRemoteConfigProvider @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseReadinessManager: FirebaseReadinessManager
) : Provider<FirebaseRemoteConfigHelper> {

    companion object {
        private const val TAG = "LazyFirebaseRemoteConfigProvider"
        private const val TIMEOUT_MS = 5000L // 5 seconds
    }

    @Volatile
    private var cachedHelper: FirebaseRemoteConfigHelper? = null

    /**
     * Gets the FirebaseRemoteConfigHelper instance, creating it only when Firebase is ready.
     * This method implements lazy instantiation with Firebase readiness checking,
     * timeout handling, and fallback to default values.
     *
     * @return FirebaseRemoteConfigHelper instance (real or fallback)
     */
    override fun get(): FirebaseRemoteConfigHelper {
        // Return cached instance if available
        cachedHelper?.let { return it }

        synchronized(this) {
            // Double-check locking pattern
            cachedHelper?.let { return it }

            Log.d(TAG, "Creating FirebaseRemoteConfigHelper, waiting for Firebase readiness...")

            // Wait for Firebase to be ready with timeout
            val isFirebaseReady = runBlocking {
                firebaseReadinessManager.waitForFirebaseReady(TIMEOUT_MS)
            }

            val helper = if (isFirebaseReady) {
                Log.d(TAG, "Firebase is ready, creating real FirebaseRemoteConfigHelper")
                try {
                    FirebaseRemoteConfigHelper(FirebaseRemoteConfig.getInstance())
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating FirebaseRemoteConfigHelper, falling back to default", e)
                    DefaultRemoteConfigHelper.create(context)
                }
            } else {
                Log.w(TAG, "Firebase not ready within timeout, using DefaultRemoteConfigHelper")
                DefaultRemoteConfigHelper.create(context)
            }

            cachedHelper = helper
            Log.d(TAG, "FirebaseRemoteConfigHelper created and cached successfully")
            return helper
        }
    }

    /**
     * Clears the cached helper instance.
     * This method is primarily for testing purposes and should not be used in production.
     */
    fun clearCache() {
        synchronized(this) {
            Log.d(TAG, "Clearing cached FirebaseRemoteConfigHelper")
            cachedHelper = null
        }
    }

    /**
     * Checks if a helper instance is currently cached.
     *
     * @return true if helper is cached, false otherwise
     */
    fun isCached(): Boolean {
        return cachedHelper != null
    }

    /**
     * Gets the current provider status for debugging.
     *
     * @return String representation of provider status
     */
    fun getProviderStatus(): String {
        val cached = cachedHelper != null
        val firebaseStatus = firebaseReadinessManager.getReadinessStatus()
        return "Provider cached: $cached, $firebaseStatus"
    }
}