package com.tqhit.battery.one.firebase

import android.util.Log
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase readiness manager that tracks Firebase initialization state and coordinates
 * async Firebase initialization. This class provides thread-safe Firebase readiness
 * tracking and blocking wait mechanism for Firebase-dependent components.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles Firebase readiness coordination
 * - Open/Closed: Extensible for additional Firebase readiness features
 * - Dependency Inversion: Provides abstractions for Firebase state management
 */
@Singleton
class FirebaseReadinessManager @Inject constructor() {

    companion object {
        private const val TAG = "FirebaseReadinessManager"
        private const val DEFAULT_TIMEOUT_MS = 5000L // 5 seconds
    }

    // Thread-safe Firebase readiness state
    private val isFirebaseReady = AtomicBoolean(false)

    // Coroutine-based completion signal for async coordination
    private val firebaseReadyDeferred = CompletableDeferred<Unit>()

    /**
     * Checks if Firebase is currently initialized and ready.
     *
     * @return true if Firebase is ready, false otherwise
     */
    fun isFirebaseInitialized(): Bo<PERSON>an {
        val ready = isFirebaseReady.get()
        Log.d(TAG, "Firebase readiness check: $ready")
        return ready
    }

    /**
     * Marks Firebase as ready after successful initialization.
     * This method should be called from BatteryApplication after Firebase initialization.
     * Thread-safe and can be called multiple times safely.
     */
    fun markFirebaseReady() {
        Log.d(TAG, "Marking Firebase as ready")

        if (isFirebaseReady.compareAndSet(false, true)) {
            Log.d(TAG, "Firebase readiness state changed to ready")
            firebaseReadyDeferred.complete(Unit)
        } else {
            Log.d(TAG, "Firebase was already marked as ready")
        }
    }

    /**
     * Waits for Firebase to be ready with a timeout.
     * This method blocks until Firebase is initialized or the timeout expires.
     *
     * @param timeoutMs Timeout in milliseconds (default: 5 seconds)
     * @return true if Firebase became ready within timeout, false if timeout occurred
     */
    suspend fun waitForFirebaseReady(timeoutMs: Long = DEFAULT_TIMEOUT_MS): Boolean {
        Log.d(TAG, "Waiting for Firebase readiness with timeout: ${timeoutMs}ms")

        // If already ready, return immediately
        if (isFirebaseReady.get()) {
            Log.d(TAG, "Firebase already ready, returning immediately")
            return true
        }

        // Wait for Firebase with timeout
        val result = withTimeoutOrNull(timeoutMs) {
            firebaseReadyDeferred.await()
            true
        } ?: false

        if (result) {
            Log.d(TAG, "Firebase became ready within timeout")
        } else {
            Log.w(TAG, "Firebase readiness timeout after ${timeoutMs}ms")
        }

        return result
    }

    /**
     * Resets the Firebase readiness state.
     * This method is primarily for testing purposes and should not be used in production.
     */
    fun resetForTesting() {
        Log.d(TAG, "Resetting Firebase readiness state for testing")
        isFirebaseReady.set(false)
        // Note: CompletableDeferred cannot be reset, but this is acceptable for testing
    }

    /**
     * Gets the current Firebase readiness status as a string for debugging.
     *
     * @return String representation of Firebase readiness status
     */
    fun getReadinessStatus(): String {
        val ready = isFirebaseReady.get()
        val deferredCompleted = firebaseReadyDeferred.isCompleted
        return "Firebase ready: $ready, Deferred completed: $deferredCompleted"
    }
}