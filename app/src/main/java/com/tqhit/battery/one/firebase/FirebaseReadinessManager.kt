package com.tqhit.battery.one.firebase

import android.util.Log
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * FirebaseReadinessManager - Singleton class that tracks Firebase initialization state
 * and coordinates async Firebase initialization.
 * 
 * This class acts as a central coordinator for Firebase-dependent components,
 * allowing them to wait for Firebase initialization without crashing.
 */
@Singleton
class FirebaseReadinessManager @Inject constructor() {
    
    companion object {
        private const val TAG = "FirebaseReadinessManager"
        private const val DEFAULT_TIMEOUT_MS = 5000L // 5 seconds
    }
    
    private val isFirebaseReady = AtomicBoolean(false)
    private val firebaseReadyDeferred = CompletableDeferred<Unit>()
    private val listeners = mutableListOf<() -> Unit>()
    
    /**
     * Marks Firebase as ready and notifies all waiting listeners
     */
    fun markFirebaseReady() {
        if (isFirebaseReady.compareAndSet(false, true)) {
            Log.d(TAG, "Firebase marked as ready")
            
            // Complete the deferred to unblock waiting coroutines
            firebaseReadyDeferred.complete(Unit)
            
            // Notify listeners
            synchronized(listeners) {
                listeners.forEach { listener ->
                    try {
                        listener.invoke()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error calling Firebase ready listener", e)
                    }
                }
                listeners.clear()
            }
        }
    }
    
    /**
     * Checks if Firebase is currently ready
     */
    fun isFirebaseReady(): Boolean {
        return isFirebaseReady.get()
    }
    
    /**
     * Waits for Firebase to be ready with a timeout.
     * Returns true if Firebase is ready within the timeout, false otherwise.
     */
    fun waitForFirebaseReady(timeoutMs: Long = DEFAULT_TIMEOUT_MS): Boolean {
        if (isFirebaseReady()) {
            return true
        }
        
        return try {
            runBlocking {
                withTimeoutOrNull(timeoutMs) {
                    firebaseReadyDeferred.await()
                } != null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error waiting for Firebase ready", e)
            false
        }
    }
    
    /**
     * Adds a listener that will be called when Firebase becomes ready
     * If Firebase is already ready, the listener is called immediately
     */
    fun addFirebaseReadyListener(listener: () -> Unit) {
        if (isFirebaseReady()) {
            listener.invoke()
        } else {
            synchronized(listeners) {
                listeners.add(listener)
            }
        }
    }
}
