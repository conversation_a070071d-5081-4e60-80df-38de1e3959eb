package com.tqhit.battery.one.di

import android.content.Context
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.firebase.FirebaseReadinessManager
import com.tqhit.battery.one.firebase.LazyFirebaseRemoteConfigProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * FirebaseSafetyModule - Hilt module that overrides the adlib SDK's Firebase dependencies
 * with safe lazy providers to prevent crashes during Firebase initialization.
 *
 * This module ensures that when <PERSON><PERSON> tries to inject FirebaseRemoteConfigHelper,
 * it gets our lazy provider instead of the direct instantiation that causes the crash.
 * The module uses higher priority to override the adlib SDK's module bindings.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles Firebase safety dependency injection
 * - Open/Closed: Extensible for additional Firebase safety components
 * - Dependency Inversion: Provides abstractions for Firebase safety management
 */
@Module
@InstallIn(SingletonComponent::class)
object FirebaseSafetyModule {

    /**
     * Provides FirebaseReadinessManager singleton instance.
     * This manager coordinates Firebase initialization state across the app.
     *
     * @return FirebaseReadinessManager singleton instance
     */
    @Provides
    @Singleton
    fun provideFirebaseReadinessManager(): FirebaseReadinessManager {
        return FirebaseReadinessManager()
    }

    /**
     * Provides LazyFirebaseRemoteConfigProvider singleton instance.
     * This provider delays FirebaseRemoteConfigHelper instantiation until Firebase is ready.
     *
     * @param context Application context for default value loading
     * @param firebaseReadinessManager Manager for Firebase readiness coordination
     * @return LazyFirebaseRemoteConfigProvider singleton instance
     */
    @Provides
    @Singleton
    fun provideLazyFirebaseRemoteConfigProvider(
        @ApplicationContext context: Context,
        firebaseReadinessManager: FirebaseReadinessManager
    ): LazyFirebaseRemoteConfigProvider {
        return LazyFirebaseRemoteConfigProvider(context, firebaseReadinessManager)
    }

    /**
     * Provides FirebaseRemoteConfigHelper with safe lazy instantiation.
     * This method overrides the adlib SDK's direct FirebaseRemoteConfigHelper provision
     * with our lazy provider that waits for Firebase to be ready.
     *
     * The @Provides annotation with higher priority ensures this binding takes precedence
     * over the adlib SDK's module binding.
     *
     * @param lazyProvider Lazy provider that handles Firebase readiness
     * @return FirebaseRemoteConfigHelper instance (real or fallback)
     */
    @Provides
    @Singleton
    fun provideFirebaseRemoteConfigHelper(
        lazyProvider: LazyFirebaseRemoteConfigProvider
    ): FirebaseRemoteConfigHelper {
        return lazyProvider.get()
    }
}