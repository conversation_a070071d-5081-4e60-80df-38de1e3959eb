package com.tqhit.battery.one.di

import android.content.Context
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.firebase.FirebaseReadinessManager
import com.tqhit.battery.one.firebase.LazyFirebaseRemoteConfigProvider
import com.tqhit.battery.one.firebase.FirebaseRemoteConfigHelperReplacer
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * FirebaseSafetyModule - Hilt module that provides safe Firebase dependencies.
 * 
 * To avoid duplicate binding conflicts with the adlib SDK's FirebaseModule, this module
 * only provides the supporting infrastructure. The actual override happens through
 * manual replacement in the application initialization.
 */
@Module
@InstallIn(SingletonComponent::class)
object FirebaseSafetyModule {
    
    /**
     * Provides the singleton FirebaseReadinessManager instance
     */
    @Provides
    @Singleton
    fun provideFirebaseReadinessManager(): FirebaseReadinessManager {
        return FirebaseReadinessManager()
    }
    
    /**
     * Provides LazyFirebaseRemoteConfigProvider for safe Firebase operations.
     * This provider will handle the safe creation of FirebaseRemoteConfigHelper.
     */
    @Provides
    @Singleton
    fun provideLazyFirebaseRemoteConfigProvider(
        @ApplicationContext context: Context,
        firebaseReadinessManager: FirebaseReadinessManager
    ): LazyFirebaseRemoteConfigProvider {
        return LazyFirebaseRemoteConfigProvider(context, firebaseReadinessManager)
    }
    
    /**
     * Provides FirebaseRemoteConfigHelperReplacer for replacing unsafe instances
     */
    @Provides
    @Singleton
    fun provideFirebaseRemoteConfigHelperReplacer(
        @ApplicationContext context: Context,
        firebaseReadinessManager: FirebaseReadinessManager
    ): FirebaseRemoteConfigHelperReplacer {
        return FirebaseRemoteConfigHelperReplacer(context, firebaseReadinessManager)
    }
}
