package com.tqhit.battery.one.firebase

import android.content.Context
import android.content.res.Resources
import android.util.Log
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import org.xmlpull.v1.XmlPullParser

/**
 * DefaultRemoteConfigHelper - Factory for creating FirebaseRemoteConfigHelper with default behavior.
 * 
 * Since FirebaseRemoteConfigHelper is final and cannot be extended, this class creates 
 * a FirebaseRemoteConfigHelper instance with a mock FirebaseRemoteConfig that returns default values.
 */
class DefaultRemoteConfigHelper private constructor() {
    
    companion object {
        private const val TAG = "DefaultRemoteConfigHelper"
        
        /**
         * Creates a FirebaseRemoteConfigHelper that returns default values
         */
        fun create(context: Context): FirebaseRemoteConfigHelper {
            Log.d(TAG, "Creating DefaultRemoteConfigHelper with fallback values")
            
            try {
                // Try to create with real Firebase instance but it will use defaults if Firebase isn't ready
                val firebaseConfig = FirebaseRemoteConfig.getInstance()
                
                // Load default values into Firebase Remote Config
                loadDefaultsIntoFirebase(context, firebaseConfig)
                
                return FirebaseRemoteConfigHelper(firebaseConfig)
            } catch (e: Exception) {
                Log.e(TAG, "Error creating FirebaseRemoteConfigHelper, using basic implementation", e)
                // This is a fallback that shouldn't normally happen
                throw IllegalStateException("Cannot create FirebaseRemoteConfigHelper", e)
            }
        }
        
        /**
         * Load default values from XML into Firebase Remote Config
         */
        private fun loadDefaultsIntoFirebase(context: Context, firebaseConfig: FirebaseRemoteConfig) {
            try {
                val defaultValues = loadDefaultValuesFromXml(context)
                if (defaultValues.isNotEmpty()) {
                    firebaseConfig.setDefaultsAsync(defaultValues)
                    Log.d(TAG, "Loaded ${defaultValues.size} default values into Firebase Remote Config")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading defaults into Firebase", e)
                // Load hardcoded defaults
                val hardcodedDefaults = getHardcodedDefaults()
                firebaseConfig.setDefaultsAsync(hardcodedDefaults)
                Log.d(TAG, "Loaded ${hardcodedDefaults.size} hardcoded default values into Firebase Remote Config")
            }
        }
        
        /**
         * Load default values from remote_config_defaults.xml
         */
        private fun loadDefaultValuesFromXml(context: Context): Map<String, Any> {
            val defaultValues = mutableMapOf<String, Any>()
            
            try {
                val resources = context.resources
                val xmlParser = resources.getXml(R.xml.remote_config_defaults)
                
                var eventType = xmlParser.eventType
                var currentKey: String? = null
                
                while (eventType != XmlPullParser.END_DOCUMENT) {
                    when (eventType) {
                        XmlPullParser.START_TAG -> {
                            if (xmlParser.name == "entry") {
                                currentKey = xmlParser.getAttributeValue(null, "key")
                            }
                        }
                        XmlPullParser.TEXT -> {
                            currentKey?.let { key ->
                                val value = xmlParser.text
                                // Try to parse as different types
                                defaultValues[key] = when {
                                    value.equals("true", ignoreCase = true) || value.equals("false", ignoreCase = true) -> 
                                        value.toBoolean()
                                    value.toLongOrNull() != null -> value.toLong()
                                    value.toDoubleOrNull() != null -> value.toDouble()
                                    else -> value
                                }
                            }
                        }
                        XmlPullParser.END_TAG -> {
                            if (xmlParser.name == "entry") {
                                currentKey = null
                            }
                        }
                    }
                    eventType = xmlParser.next()
                }
                
                Log.d(TAG, "Loaded ${defaultValues.size} default values from XML")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading default values from XML", e)
            }
            
            return defaultValues
        }
        
        /**
         * Get hardcoded default values as ultimate fallback
         */
        private fun getHardcodedDefaults(): Map<String, Any> {
            return mapOf(
                // Ad configuration defaults
                "aoa_enable" to true,
                "aoa_enable_first_session" to false,
                "aoa_show_frequency" to 30L,
                
                "iv_enable" to true,
                "iv_first_session_load_delay" to 3L,
                "iv_load_delay" to 2L,
                "iv_first_session_show_delay" to 10L,
                "iv_show_delay" to 5L,
                "iv_show_frequency" to 30L,
                "iv_show_delay_after_RV" to 10L,
                
                "rv_enable" to true,
                "rv_load_in_view" to true,
                "rv_load_delay" to 1L,
                
                "nt_enable" to true,
                "bn_enable" to true,
                
                // Animation configuration defaults
                "animation_json" to "{}",
                
                // Onboarding configuration
                "onboarding_next_delay" to 1000L
            )
        }
    }
}
