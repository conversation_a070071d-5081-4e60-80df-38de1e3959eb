package com.tqhit.battery.one.firebase

import android.content.Context
import android.content.res.Resources
import android.util.Log
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import org.xmlpull.v1.XmlPullParser

/**
 * DefaultRemoteConfigHelper - Factory for creating FirebaseRemoteConfigHelper with default behavior.
 *
 * Since FirebaseRemoteConfigHelper is final and cannot be extended, this class creates
 * a FirebaseRemoteConfigHelper instance with a mock FirebaseRemoteConfig that returns default values.
 * This helper serves as a fallback when Firebase initialization times out or fails.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles default remote config value provision
 * - Open/Closed: Extensible for additional default value sources
 * - Dependency Inversion: Provides abstractions for remote config defaults
 */
class DefaultRemoteConfigHelper private constructor() {

    companion object {
        private const val TAG = "DefaultRemoteConfigHelper"

        /**
         * Creates a FirebaseRemoteConfigHelper that returns default values.
         * This method loads defaults from remote_config_defaults.xml and creates
         * a FirebaseRemoteConfigHelper instance that will use these defaults.
         *
         * @param context Application context for loading XML defaults
         * @return FirebaseRemoteConfigHelper instance with default values
         */
        fun create(context: Context): FirebaseRemoteConfigHelper {
            Log.d(TAG, "Creating DefaultRemoteConfigHelper with fallback values")

            try {
                // Try to create with real Firebase instance but it will use defaults if Firebase isn't ready
                val firebaseConfig = FirebaseRemoteConfig.getInstance()

                // Load default values into Firebase Remote Config
                loadDefaultsIntoFirebase(context, firebaseConfig)

                return FirebaseRemoteConfigHelper(firebaseConfig)
            } catch (e: Exception) {
                Log.e(TAG, "Error creating FirebaseRemoteConfigHelper, using basic implementation", e)
                // This is a fallback that shouldn't normally happen
                throw IllegalStateException("Cannot create FirebaseRemoteConfigHelper", e)
            }
        }

        /**
         * Load default values from XML into Firebase Remote Config.
         * This ensures that even if Firebase isn't fully initialized,
         * the remote config will return sensible default values.
         *
         * @param context Application context for loading XML
         * @param firebaseConfig Firebase Remote Config instance to load defaults into
         */
        private fun loadDefaultsIntoFirebase(context: Context, firebaseConfig: FirebaseRemoteConfig) {
            try {
                val defaultValues = loadDefaultValuesFromXml(context)
                if (defaultValues.isNotEmpty()) {
                    firebaseConfig.setDefaultsAsync(defaultValues)
                    Log.d(TAG, "Loaded ${defaultValues.size} default values into Firebase Remote Config")
                } else {
                    Log.w(TAG, "No default values loaded from XML, using hardcoded defaults")
                    val hardcodedDefaults = getHardcodedDefaults()
                    firebaseConfig.setDefaultsAsync(hardcodedDefaults)
                    Log.d(TAG, "Loaded ${hardcodedDefaults.size} hardcoded default values into Firebase Remote Config")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading defaults into Firebase", e)
                // Load hardcoded defaults as ultimate fallback
                val hardcodedDefaults = getHardcodedDefaults()
                firebaseConfig.setDefaultsAsync(hardcodedDefaults)
                Log.d(TAG, "Loaded ${hardcodedDefaults.size} hardcoded default values into Firebase Remote Config")
            }
        }

        /**
         * Load default values from remote_config_defaults.xml.
         * This method parses the XML file and extracts all key-value pairs.
         *
         * @param context Application context for accessing resources
         * @return Map of default values from XML
         */
        private fun loadDefaultValuesFromXml(context: Context): Map<String, Any> {
            val defaultValues = mutableMapOf<String, Any>()

            try {
                val resources: Resources = context.resources
                val parser: XmlPullParser = resources.getXml(R.xml.remote_config_defaults)

                var eventType = parser.eventType
                var currentKey: String? = null

                while (eventType != XmlPullParser.END_DOCUMENT) {
                    when (eventType) {
                        XmlPullParser.START_TAG -> {
                            when (parser.name) {
                                "key" -> {
                                    // Next text will be the key
                                }
                                "value" -> {
                                    // Next text will be the value
                                }
                            }
                        }
                        XmlPullParser.TEXT -> {
                            val text = parser.text?.trim()
                            if (!text.isNullOrEmpty()) {
                                if (currentKey == null) {
                                    // This text is a key
                                    currentKey = text
                                } else {
                                    // This text is a value for the current key
                                    val value = parseValue(text)
                                    defaultValues[currentKey] = value
                                    Log.d(TAG, "Loaded default: $currentKey = $value")
                                    currentKey = null
                                }
                            }
                        }
                        XmlPullParser.END_TAG -> {
                            if (parser.name == "entry") {
                                currentKey = null // Reset for next entry
                            }
                        }
                    }
                    eventType = parser.next()
                }

                Log.d(TAG, "Successfully loaded ${defaultValues.size} default values from XML")
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing remote_config_defaults.xml", e)
            }

            return defaultValues
        }

        /**
         * Parse a string value to appropriate type (String, Boolean, Long).
         * This method attempts to convert string values to their appropriate types
         * based on common patterns.
         *
         * @param value String value to parse
         * @return Parsed value as appropriate type
         */
        private fun parseValue(value: String): Any {
            return when {
                value.equals("true", ignoreCase = true) -> true
                value.equals("false", ignoreCase = true) -> false
                value.toLongOrNull() != null -> value.toLong()
                value.toDoubleOrNull() != null -> value.toDouble()
                else -> value // Keep as string
            }
        }

        /**
         * Get hardcoded default values as ultimate fallback.
         * These values match those in remote_config_defaults.xml and ensure
         * the app can function even if XML loading fails.
         *
         * @return Map of hardcoded default values
         */
        private fun getHardcodedDefaults(): Map<String, Any> {
            return mapOf(
                // Ad configuration defaults
                "aoa_enable" to true,
                "aoa_enable_first_session" to false,
                "aoa_show_frequency" to 120L,

                "iv_enable" to true,
                "iv_first_session_load_delay" to 0L,
                "iv_load_delay" to 0L,
                "iv_first_session_show_delay" to 0L,
                "iv_show_delay" to 0L,
                "iv_show_frequency" to 30L,
                "iv_show_delay_after_RV" to 180L,

                "rv_enable" to true,
                "rv_load_in_view" to false,
                "rv_load_delay" to 90L,

                "nt_enable" to true,
                "bn_enable" to true,

                // Animation configuration defaults
                "animation_json" to "[]",

                // Onboarding configuration
                "onboarding_next_delay" to 2000L
            )
        }
    }
}